import React from 'react';
import { Container, <PERSON>, <PERSON> } from 'react-bootstrap';

const Footer = () => {
  return (
    <footer className="bg-dark text-light py-4 mt-auto">
      <Container>
        <Row className="g-4">
          <Col lg={4} md={6} className="mb-4">
            <h5 className="text-warning fw-bold mb-3">ALTIUS 2K25</h5>
            <p className="text-muted mb-3">
              The ultimate college fest bringing together technology, culture, sports,
              and creativity. Join us for an unforgettable experience filled with
              competitions, performances, and networking opportunities.
            </p>
          </Col>

          <Col lg={2} md={6} sm={6} className="mb-4">
            <h6 className="text-warning fw-semibold mb-3">Quick Links</h6>
            <ul className="list-unstyled">
              <li className="mb-2"><a href="/" className="text-muted text-decoration-none hover-warning">Home</a></li>
              <li className="mb-2"><a href="/events" className="text-muted text-decoration-none hover-warning">Events</a></li>
              <li className="mb-2"><a href="/gallery" className="text-muted text-decoration-none hover-warning">Gallery</a></li>
              <li className="mb-2"><a href="/register" className="text-muted text-decoration-none hover-warning">Register</a></li>
            </ul>
          </Col>
          
          <Col lg={2} md={6} sm={6} className="mb-4">
            <h6 className="text-warning fw-semibold mb-3">Categories</h6>
            <ul className="list-unstyled">
              <li className="mb-2"><a href="/events?category=Technical" className="text-muted text-decoration-none hover-warning">Technical</a></li>
              <li className="mb-2"><a href="/events?category=Cultural" className="text-muted text-decoration-none hover-warning">Cultural</a></li>
              <li className="mb-2"><a href="/events?category=Sports" className="text-muted text-decoration-none hover-warning">Sports</a></li>
              <li className="mb-2"><a href="/events?category=Gaming" className="text-muted text-decoration-none hover-warning">Gaming</a></li>
            </ul>
          </Col>

          <Col lg={4} md={12} className="mb-4">
            <h6 className="text-warning fw-semibold mb-3">Contact Info</h6>
            <div className="text-muted mb-3">
              <p className="mb-2 d-flex align-items-center">
                <i className="fas fa-map-marker-alt me-2 text-warning"></i>
                College Campus, Main Auditorium
              </p>
              <p className="mb-2 d-flex align-items-center">
                <i className="fas fa-envelope me-2 text-warning"></i>
                <a href="mailto:<EMAIL>" className="text-muted text-decoration-none"><EMAIL></a>
              </p>
              <p className="mb-2 d-flex align-items-center">
                <i className="fas fa-phone me-2 text-warning"></i>
                <a href="tel:+919876543210" className="text-muted text-decoration-none">+91 98765 43210</a>
              </p>
            </div>

            <div>
              <h6 className="text-warning fw-semibold mb-3">Follow Us</h6>
              <div className="d-flex gap-3">
                <a href="#" className="text-muted hover-warning fs-5" title="Facebook">
                  <i className="fab fa-facebook-f"></i>
                </a>
                <a href="#" className="text-muted hover-warning fs-5" title="Twitter">
                  <i className="fab fa-twitter"></i>
                </a>
                <a href="#" className="text-muted hover-warning fs-5" title="Instagram">
                  <i className="fab fa-instagram"></i>
                </a>
                <a href="#" className="text-muted hover-warning fs-5" title="LinkedIn">
                  <i className="fab fa-linkedin-in"></i>
                </a>
              </div>
            </div>
          </Col>
        </Row>
        
        <hr className="my-3 border-secondary" />

        <Row className="align-items-center">
          <Col md={6} className="text-center text-md-start">
            <p className="text-muted mb-2 mb-md-0">
              &copy; 2025 ALTIUS 2K25. All rights reserved.
            </p>
          </Col>
          <Col md={6} className="text-center text-md-end">
            <p className="text-muted mb-0">
              Made with <i className="fas fa-heart text-danger"></i> by the ALTIUS Team
            </p>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;
