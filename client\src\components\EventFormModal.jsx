import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, Row, Col, Alert } from 'react-bootstrap';
import { adminAPI } from '../services/api';
import { toast } from 'react-toastify';

const EventFormModal = ({ show, onHide, event = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    rules: '',
    category: 'Technical',
    contactName: '',
    contactEmail: '',
    contactPhone: '',
    maxParticipants: 100,
    eventDate: '',
    venue: '',
    poster: null,
    isActive: true
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const categories = ['Technical', 'Cultural', 'Sports', 'Workshop', 'Competition'];

  useEffect(() => {
    if (event) {
      // Editing existing event
      setFormData({
        title: event.title || '',
        description: event.description || '',
        rules: event.rules || '',
        category: event.category || 'Technical',
        contactName: event.contactInfo?.name || '',
        contactEmail: event.contactInfo?.email || '',
        contactPhone: event.contactInfo?.phone || '',
        maxParticipants: event.maxParticipants || 100,
        eventDate: event.eventDate ? new Date(event.eventDate).toISOString().slice(0, 16) : '',
        venue: event.venue || '',
        poster: null,
        isActive: event.isActive !== undefined ? event.isActive : true
      });
    } else {
      // Creating new event
      setFormData({
        title: '',
        description: '',
        rules: '',
        category: 'Technical',
        contactName: '',
        contactEmail: '',
        contactPhone: '',
        maxParticipants: 100,
        eventDate: '',
        venue: '',
        poster: null,
        isActive: true
      });
    }
    setErrors({});
  }, [event, show]);

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    
    if (type === 'file') {
      setFormData(prev => ({ ...prev, [name]: files[0] }));
    } else if (type === 'checkbox') {
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.rules.trim()) newErrors.rules = 'Rules are required';
    if (!formData.contactName.trim()) newErrors.contactName = 'Contact name is required';
    if (!formData.contactEmail.trim()) newErrors.contactEmail = 'Contact email is required';
    if (!formData.contactPhone.trim()) newErrors.contactPhone = 'Contact phone is required';
    if (!formData.eventDate) newErrors.eventDate = 'Event date is required';
    if (!formData.venue.trim()) newErrors.venue = 'Venue is required';
    if (!event && !formData.poster) newErrors.poster = 'Poster image is required for new events';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.contactEmail && !emailRegex.test(formData.contactEmail)) {
      newErrors.contactEmail = 'Please enter a valid email address';
    }

    // Phone validation
    const phoneRegex = /^[0-9]{10}$/;
    if (formData.contactPhone && !phoneRegex.test(formData.contactPhone.replace(/\D/g, ''))) {
      newErrors.contactPhone = 'Please enter a valid 10-digit phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const submitData = {
        ...formData,
        contactName: formData.contactName,
        contactEmail: formData.contactEmail,
        contactPhone: formData.contactPhone
      };

      if (event) {
        // Update existing event
        await adminAPI.updateEvent(event.id, submitData);
        toast.success('Event updated successfully!');
      } else {
        // Create new event
        await adminAPI.createEvent(submitData);
        toast.success('Event created successfully!');
      }
      
      onSuccess && onSuccess();
      onHide();
    } catch (error) {
      const message = error.response?.data?.message || `Failed to ${event ? 'update' : 'create'} event`;
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>{event ? 'Edit Event' : 'Create New Event'}</Modal.Title>
      </Modal.Header>
      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Event Title *</Form.Label>
                <Form.Control
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  isInvalid={!!errors.title}
                  placeholder="Enter event title"
                />
                <Form.Control.Feedback type="invalid">
                  {errors.title}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Category *</Form.Label>
                <Form.Select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                >
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>

          <Form.Group className="mb-3">
            <Form.Label>Description *</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="description"
              value={formData.description}
              onChange={handleChange}
              isInvalid={!!errors.description}
              placeholder="Enter event description"
            />
            <Form.Control.Feedback type="invalid">
              {errors.description}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Rules *</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="rules"
              value={formData.rules}
              onChange={handleChange}
              isInvalid={!!errors.rules}
              placeholder="Enter event rules"
            />
            <Form.Control.Feedback type="invalid">
              {errors.rules}
            </Form.Control.Feedback>
          </Form.Group>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Event Date *</Form.Label>
                <Form.Control
                  type="datetime-local"
                  name="eventDate"
                  value={formData.eventDate}
                  onChange={handleChange}
                  isInvalid={!!errors.eventDate}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.eventDate}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Venue *</Form.Label>
                <Form.Control
                  type="text"
                  name="venue"
                  value={formData.venue}
                  onChange={handleChange}
                  isInvalid={!!errors.venue}
                  placeholder="Enter venue"
                />
                <Form.Control.Feedback type="invalid">
                  {errors.venue}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Contact Name *</Form.Label>
                <Form.Control
                  type="text"
                  name="contactName"
                  value={formData.contactName}
                  onChange={handleChange}
                  isInvalid={!!errors.contactName}
                  placeholder="Contact person name"
                />
                <Form.Control.Feedback type="invalid">
                  {errors.contactName}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Contact Email *</Form.Label>
                <Form.Control
                  type="email"
                  name="contactEmail"
                  value={formData.contactEmail}
                  onChange={handleChange}
                  isInvalid={!!errors.contactEmail}
                  placeholder="<EMAIL>"
                />
                <Form.Control.Feedback type="invalid">
                  {errors.contactEmail}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Contact Phone *</Form.Label>
                <Form.Control
                  type="tel"
                  name="contactPhone"
                  value={formData.contactPhone}
                  onChange={handleChange}
                  isInvalid={!!errors.contactPhone}
                  placeholder="1234567890"
                />
                <Form.Control.Feedback type="invalid">
                  {errors.contactPhone}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Max Participants</Form.Label>
                <Form.Control
                  type="number"
                  name="maxParticipants"
                  value={formData.maxParticipants}
                  onChange={handleChange}
                  min="1"
                  placeholder="100"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Poster Image {!event && '*'}</Form.Label>
                <Form.Control
                  type="file"
                  name="poster"
                  onChange={handleChange}
                  accept="image/*"
                  isInvalid={!!errors.poster}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.poster}
                </Form.Control.Feedback>
                {event && (
                  <Form.Text className="text-muted">
                    Leave empty to keep current poster
                  </Form.Text>
                )}
              </Form.Group>
            </Col>
          </Row>

          {event && (
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                name="isActive"
                checked={formData.isActive}
                onChange={handleChange}
                label="Event is active"
              />
            </Form.Group>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={loading}>
            Cancel
          </Button>
          <Button variant="primary" type="submit" disabled={loading}>
            {loading ? (event ? 'Updating...' : 'Creating...') : (event ? 'Update Event' : 'Create Event')}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default EventFormModal;
