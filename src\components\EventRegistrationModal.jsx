import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, Row, Col, Alert } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';
import { eventsAPI } from '../services/api';
import { formatDate } from '../utils/helpers';
import { toast } from 'react-toastify';

const EventRegistrationModal = ({ show, onHide, event, onSuccess }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    usn: '',
    department: '',
    year: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const departments = [
    'Computer Science and Engineering',
    'Information Science and Engineering',
    'Electronics and Communication Engineering',
    'Electrical and Electronics Engineering',
    'Mechanical Engineering',
    'Civil Engineering',
    'Chemical Engineering',
    'Biotechnology',
    'Aerospace Engineering',
    'Industrial Engineering',
    'Other'
  ];

  const years = ['1st Year', '2nd Year', '3rd Year', '4th Year', 'PG 1st Year', 'PG 2nd Year'];

  useEffect(() => {
    if (show && user) {
      setFormData({
        name: user.name || '',
        usn: user.usn || '',
        department: user.department || '',
        year: user.year || ''
      });
      setErrors({});
    }
  }, [show, user]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.usn.trim()) {
      newErrors.usn = 'USN is required';
    } else if (formData.usn.length < 3) {
      newErrors.usn = 'USN must be at least 3 characters';
    }

    if (!formData.department) {
      newErrors.department = 'Department is required';
    }

    if (!formData.year) {
      newErrors.year = 'Year is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await eventsAPI.registerForEvent(event._id, {
        usn: formData.usn.trim(),
        department: formData.department,
        year: formData.year
      });
      
      toast.success('Successfully registered for the event!');
      onSuccess && onSuccess();
      onHide();
    } catch (error) {
      const message = error.response?.data?.message || 'Registration failed';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  if (!event) return null;

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          <i className="fas fa-user-plus me-2"></i>
          Event Registration
        </Modal.Title>
      </Modal.Header>
      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          {/* Event Information */}
          <div className="mb-4 p-3 bg-light rounded">
            <h5 className="mb-2 text-primary">{event.title}</h5>
            <Row>
              <Col md={6}>
                <small className="text-muted d-block">
                  <i className="fas fa-calendar me-1"></i>
                  <strong>Date:</strong> {formatDate(event.eventDate)}
                </small>
                <small className="text-muted d-block">
                  <i className="fas fa-map-marker-alt me-1"></i>
                  <strong>Venue:</strong> {event.venue}
                </small>
              </Col>
              <Col md={6}>
                <small className="text-muted d-block">
                  <i className="fas fa-users me-1"></i>
                  <strong>Max Participants:</strong> {event.maxParticipants}
                </small>
                <small className="text-muted d-block">
                  <i className="fas fa-user-check me-1"></i>
                  <strong>Available Spots:</strong> {event.availableSpots || 'N/A'}
                </small>
              </Col>
            </Row>
          </div>

          <Alert variant="info" className="mb-4">
            <i className="fas fa-info-circle me-2"></i>
            Please fill in your details to complete the registration. Your name is pre-filled from your profile.
          </Alert>

          {/* Registration Form */}
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Full Name *</Form.Label>
                <Form.Control
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  isInvalid={!!errors.name}
                  placeholder="Enter your full name"
                  readOnly
                  className="bg-light"
                />
                <Form.Control.Feedback type="invalid">
                  {errors.name}
                </Form.Control.Feedback>
                <Form.Text className="text-muted">
                  Name is taken from your profile
                </Form.Text>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>USN (University Seat Number) *</Form.Label>
                <Form.Control
                  type="text"
                  name="usn"
                  value={formData.usn}
                  onChange={handleChange}
                  isInvalid={!!errors.usn}
                  placeholder="e.g., 1AB21CS001"
                  style={{ textTransform: 'uppercase' }}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.usn}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Department *</Form.Label>
                <Form.Select
                  name="department"
                  value={formData.department}
                  onChange={handleChange}
                  isInvalid={!!errors.department}
                >
                  <option value="">Select your department</option>
                  {departments.map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </Form.Select>
                <Form.Control.Feedback type="invalid">
                  {errors.department}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Year of Study *</Form.Label>
                <Form.Select
                  name="year"
                  value={formData.year}
                  onChange={handleChange}
                  isInvalid={!!errors.year}
                >
                  <option value="">Select your year</option>
                  {years.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </Form.Select>
                <Form.Control.Feedback type="invalid">
                  {errors.year}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          {/* Contact Information */}
          <div className="mt-4 p-3 border rounded">
            <h6 className="mb-2">Event Contact Information</h6>
            <Row>
              <Col md={6}>
                <small className="text-muted d-block">
                  <strong>Contact Person:</strong> {event.contactInfo?.name}
                </small>
                <small className="text-muted d-block">
                  <strong>Email:</strong> {event.contactInfo?.email}
                </small>
              </Col>
              <Col md={6}>
                <small className="text-muted d-block">
                  <strong>Phone:</strong> {event.contactInfo?.phone}
                </small>
              </Col>
            </Row>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={loading}>
            Cancel
          </Button>
          <Button 
            variant="success" 
            type="submit" 
            disabled={loading}
            className="fw-semibold"
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Registering...
              </>
            ) : (
              <>
                <i className="fas fa-check me-2"></i>
                Complete Registration
              </>
            )}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default EventRegistrationModal;
