import React, { useState } from 'react';
import { Navbar, Nav, Container, But<PERSON>, Offcanvas } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const NavigationBar = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const location = useLocation();
  const [show, setShow] = useState(false);

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const handleLogout = () => {
    logout();
    handleClose();
  };

  const isActive = (path) => location.pathname === path;

  return (
    <>
      <Navbar bg="dark" variant="dark" expand="lg" fixed="top" className="shadow">
        <Container>
          <Navbar.Brand as={Link} to="/" className="fw-bold fs-3 text-gradient">
            <span className="text-warning">ALTIUS</span>
            <span className="text-light"> 2K25</span>
          </Navbar.Brand>

          <Button
            variant="outline-light"
            className="d-lg-none"
            onClick={handleShow}
          >
            <i className="fas fa-bars"></i>
          </Button>

          <Navbar.Collapse id="basic-navbar-nav" className="d-none d-lg-flex">
            <Nav className="me-auto">
              <Nav.Link
                as={Link}
                to="/"
                className={`fw-semibold ${isActive('/') ? 'text-warning' : ''}`}
              >
                Home
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/events"
                className={`fw-semibold ${isActive('/events') ? 'text-warning' : ''}`}
              >
                Events
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/gallery"
                className={`fw-semibold ${isActive('/gallery') ? 'text-warning' : ''}`}
              >
                Gallery
              </Nav.Link>
            </Nav>

            <Nav>
              {isAuthenticated ? (
                <>
                  <Nav.Link
                    as={Link}
                    to="/dashboard"
                    className={`fw-semibold ${isActive('/dashboard') ? 'text-warning' : ''}`}
                  >
                    <i className="fas fa-user me-1"></i>
                    {user?.name}
                  </Nav.Link>
                  {user?.isAdmin && (
                    <Nav.Link
                      as={Link}
                      to="/admin"
                      className={`fw-semibold ${isActive('/admin') ? 'text-warning' : ''}`}
                    >
                      <i className="fas fa-cog me-1"></i>
                      Admin
                    </Nav.Link>
                  )}
                  <Button
                    variant="outline-warning"
                    size="sm"
                    onClick={handleLogout}
                    className="ms-2"
                  >
                    Logout
                  </Button>
                </>
              ) : (
                <>
                  <Nav.Link
                    as={Link}
                    to="/login"
                    className={`fw-semibold ${isActive('/login') ? 'text-warning' : ''}`}
                  >
                    Login
                  </Nav.Link>
                  <Button
                    as={Link}
                    to="/register"
                    variant="warning"
                    size="sm"
                    className="ms-2 text-dark fw-semibold"
                  >
                    Register
                  </Button>
                </>
              )}
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      {/* Mobile Offcanvas Menu */}
      <Offcanvas show={show} onHide={handleClose} placement="end" className="bg-dark text-light">
        <Offcanvas.Header closeButton closeVariant="white">
          <Offcanvas.Title className="text-warning fw-bold">
            ALTIUS 2K25
          </Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body>
          <Nav className="flex-column">
            <Nav.Link
              as={Link}
              to="/"
              className={`fw-semibold py-3 ${isActive('/') ? 'text-warning' : 'text-light'}`}
              onClick={handleClose}
            >
              <i className="fas fa-home me-2"></i>
              Home
            </Nav.Link>
            <Nav.Link
              as={Link}
              to="/events"
              className={`fw-semibold py-3 ${isActive('/events') ? 'text-warning' : 'text-light'}`}
              onClick={handleClose}
            >
              <i className="fas fa-calendar me-2"></i>
              Events
            </Nav.Link>
            <Nav.Link
              as={Link}
              to="/gallery"
              className={`fw-semibold py-3 ${isActive('/gallery') ? 'text-warning' : 'text-light'}`}
              onClick={handleClose}
            >
              <i className="fas fa-images me-2"></i>
              Gallery
            </Nav.Link>

            <hr className="my-3" />

            {isAuthenticated ? (
              <>
                <Nav.Link
                  as={Link}
                  to="/dashboard"
                  className={`fw-semibold py-3 ${isActive('/dashboard') ? 'text-warning' : 'text-light'}`}
                  onClick={handleClose}
                >
                  <i className="fas fa-user me-2"></i>
                  Dashboard
                </Nav.Link>
                {user?.isAdmin && (
                  <Nav.Link
                    as={Link}
                    to="/admin"
                    className={`fw-semibold py-3 ${isActive('/admin') ? 'text-warning' : 'text-light'}`}
                    onClick={handleClose}
                  >
                    <i className="fas fa-cog me-2"></i>
                    Admin Panel
                  </Nav.Link>
                )}
                <Button
                  variant="outline-warning"
                  className="mt-3"
                  onClick={handleLogout}
                >
                  <i className="fas fa-sign-out-alt me-2"></i>
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Nav.Link
                  as={Link}
                  to="/login"
                  className={`fw-semibold py-3 ${isActive('/login') ? 'text-warning' : 'text-light'}`}
                  onClick={handleClose}
                >
                  <i className="fas fa-sign-in-alt me-2"></i>
                  Login
                </Nav.Link>
                <Button
                  as={Link}
                  to="/register"
                  variant="warning"
                  className="mt-3 text-dark fw-semibold"
                  onClick={handleClose}
                >
                  <i className="fas fa-user-plus me-2"></i>
                  Register
                </Button>
              </>
            )}
          </Nav>
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
};

export default NavigationBar;
