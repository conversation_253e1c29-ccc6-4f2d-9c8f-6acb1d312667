import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Badge, <PERSON><PERSON>, Spin<PERSON>, Form, InputGroup } from 'react-bootstrap';
import { adminAPI } from '../services/api';
import { formatDate } from '../utils/helpers';
import { toast } from 'react-toastify';

const ParticipantsModal = ({ show, onHide, event }) => {
  const [participants, setParticipants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [eventInfo, setEventInfo] = useState(null);

  useEffect(() => {
    if (show && event) {
      fetchParticipants();
    }
  }, [show, event]);

  const fetchParticipants = async () => {
    if (!event?.id) return;
    
    setLoading(true);
    try {
      const response = await adminAPI.getEventParticipants(event.id);
      setParticipants(response.data.participants);
      setEventInfo(response.data.event);
    } catch (error) {
      console.error('Error fetching participants:', error);
      toast.error('Failed to load participants');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveParticipant = async (userId, userName) => {
    if (!window.confirm(`Are you sure you want to remove ${userName} from this event?`)) {
      return;
    }

    try {
      await adminAPI.removeParticipant(event.id, userId);
      toast.success(`${userName} has been removed from the event`);
      fetchParticipants(); // Refresh the list
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to remove participant';
      toast.error(message);
    }
  };

  const filteredParticipants = participants.filter(participant =>
    participant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    participant.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const exportParticipants = () => {
    try {
      const csvContent = [
        ['Name', 'Email', 'Registration Date'],
        ...participants.map(p => [
          p.name,
          p.email,
          formatDate(p.registeredAt)
        ])
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${event.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_participants.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success('Participants list exported successfully!');
    } catch (error) {
      toast.error('Failed to export participants list');
    }
  };

  if (!event) return null;

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          <i className="fas fa-users me-2"></i>
          Event Participants
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {/* Event Info */}
        <div className="mb-4 p-3 bg-light rounded">
          <h5 className="mb-2">{event.title}</h5>
          <div className="row">
            <div className="col-md-6">
              <small className="text-muted">
                <strong>Total Registrations:</strong> {eventInfo?.registrationCount || 0}
              </small>
            </div>
            <div className="col-md-6">
              <small className="text-muted">
                <strong>Max Participants:</strong> {eventInfo?.maxParticipants || 0}
              </small>
            </div>
          </div>
          <div className="mt-2">
            <div className="progress" style={{ height: '6px' }}>
              <div
                className="progress-bar bg-success"
                style={{
                  width: `${((eventInfo?.registrationCount || 0) / (eventInfo?.maxParticipants || 1)) * 100}%`
                }}
              ></div>
            </div>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="d-flex justify-content-between align-items-center mb-3">
          <InputGroup style={{ maxWidth: '300px' }}>
            <InputGroup.Text>
              <i className="fas fa-search"></i>
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="Search participants..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
          
          <div className="d-flex gap-2">
            <Button
              variant="outline-success"
              size="sm"
              onClick={exportParticipants}
              disabled={participants.length === 0}
            >
              <i className="fas fa-download me-1"></i>
              Export CSV
            </Button>
            <Button
              variant="outline-primary"
              size="sm"
              onClick={fetchParticipants}
              disabled={loading}
            >
              <i className="fas fa-sync-alt me-1"></i>
              Refresh
            </Button>
          </div>
        </div>

        {/* Participants Table */}
        {loading ? (
          <div className="text-center py-4">
            <Spinner animation="border" variant="primary" />
            <p className="mt-2 text-muted">Loading participants...</p>
          </div>
        ) : participants.length === 0 ? (
          <Alert variant="info" className="text-center">
            <i className="fas fa-info-circle me-2"></i>
            No participants registered for this event yet.
          </Alert>
        ) : (
          <>
            <div className="mb-2">
              <small className="text-muted">
                Showing {filteredParticipants.length} of {participants.length} participants
              </small>
            </div>
            <div className="table-responsive" style={{ maxHeight: '400px', overflowY: 'auto' }}>
              <Table hover size="sm">
                <thead className="bg-light sticky-top">
                  <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Registered Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredParticipants.map((participant, index) => (
                    <tr key={participant.id}>
                      <td>{index + 1}</td>
                      <td>
                        <div className="fw-semibold">{participant.name}</div>
                      </td>
                      <td>
                        <a href={`mailto:${participant.email}`} className="text-decoration-none">
                          {participant.email}
                        </a>
                      </td>
                      <td>
                        <small className="text-muted">
                          {formatDate(participant.registeredAt)}
                        </small>
                      </td>
                      <td>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleRemoveParticipant(participant.id, participant.name)}
                          title="Remove participant"
                        >
                          <i className="fas fa-user-minus"></i>
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        <div className="d-flex justify-content-between align-items-center w-100">
          <div>
            {participants.length > 0 && (
              <Badge bg="primary">
                {participants.length} participant{participants.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>
          <Button variant="secondary" onClick={onHide}>
            Close
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default ParticipantsModal;
