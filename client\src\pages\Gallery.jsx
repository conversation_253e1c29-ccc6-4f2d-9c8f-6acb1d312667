import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Modal, Form, Button } from 'react-bootstrap';
import { eventsAPI } from '../services/api';
import Loading from '../components/Loading';
import { getCategoryColor } from '../utils/helpers';

const Gallery = () => {
  const [events, setEvents] = useState([]);
  const [filteredEvents, setFilteredEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState('All');

  const categories = ['All', 'Technical', 'Cultural', 'Sports', 'Literary', 'Gaming', 'Other'];

  useEffect(() => {
    fetchEvents();
  }, []);

  useEffect(() => {
    filterEvents();
  }, [events, categoryFilter]);

  const fetchEvents = async () => {
    try {
      const response = await eventsAPI.getAllEvents({ limit: 50 });
      setEvents(response.data.events);
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterEvents = () => {
    if (categoryFilter === 'All') {
      setFilteredEvents(events);
    } else {
      setFilteredEvents(events.filter(event => event.category === categoryFilter));
    }
  };

  const handleImageClick = (event) => {
    setSelectedImage(event);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedImage(null);
  };

  if (loading) {
    return <Loading fullScreen text="Loading gallery..." />;
  }

  return (
    <div className="gallery-page py-5">
      <Container>
        {/* Header */}
        <Row className="mb-5">
          <Col className="text-center">
            <h1 className="display-4 fw-bold text-dark mb-3">Event Gallery</h1>
            <p className="lead text-muted">
              Explore the vibrant posters and visuals of ALTIUS 2K25 events
            </p>
          </Col>
        </Row>

        {/* Filter */}
        <Row className="mb-4">
          <Col md={6} className="mx-auto">
            <Form.Select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="text-center"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'All' ? 'All Categories' : category}
                </option>
              ))}
            </Form.Select>
          </Col>
        </Row>

        {/* Gallery Grid */}
        {filteredEvents.length === 0 ? (
          <Row>
            <Col className="text-center py-5">
              <i className="fas fa-images display-1 text-muted mb-3"></i>
              <h4 className="text-muted">No events found</h4>
              <p className="text-muted">
                {categoryFilter === 'All' 
                  ? 'No events available in the gallery'
                  : `No events found in ${categoryFilter} category`
                }
              </p>
            </Col>
          </Row>
        ) : (
          <Row className="g-4">
            {filteredEvents.map((event) => (
              <Col lg={4} md={6} key={event._id}>
                <Card className="gallery-card h-100 shadow-sm hover-shadow">
                  <div className="position-relative overflow-hidden">
                    <Card.Img
                      variant="top"
                      src={`http://localhost:5000${event.posterURL}`}
                      alt={event.title}
                      className="gallery-image"
                      style={{ 
                        height: '250px', 
                        objectFit: 'cover',
                        cursor: 'pointer',
                        transition: 'transform 0.3s ease'
                      }}
                      onClick={() => handleImageClick(event)}
                      onMouseEnter={(e) => {
                        e.target.style.transform = 'scale(1.05)';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.transform = 'scale(1)';
                      }}
                    />
                    <div className="position-absolute top-0 start-0 m-2">
                      <span className={`badge bg-${getCategoryColor(event.category)}`}>
                        {event.category}
                      </span>
                    </div>
                    <div className="gallery-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                      <Button
                        variant="light"
                        className="rounded-circle"
                        onClick={() => handleImageClick(event)}
                      >
                        <i className="fas fa-expand-alt"></i>
                      </Button>
                    </div>
                  </div>
                  <Card.Body>
                    <Card.Title className="fw-bold text-dark mb-2 text-truncate">
                      {event.title}
                    </Card.Title>
                    <Card.Text className="text-muted small mb-2">
                      <i className="fas fa-calendar me-1"></i>
                      {new Date(event.eventDate).toLocaleDateString()}
                    </Card.Text>
                    <Card.Text className="text-muted small">
                      <i className="fas fa-map-marker-alt me-1"></i>
                      {event.venue}
                    </Card.Text>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        )}

        {/* Image Modal */}
        <Modal 
          show={showModal} 
          onHide={handleCloseModal} 
          size="xl" 
          centered
          className="gallery-modal"
        >
          <Modal.Header closeButton className="border-0">
            <Modal.Title className="fw-bold">
              {selectedImage?.title}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="text-center p-0">
            {selectedImage && (
              <div className="position-relative">
                <img
                  src={`http://localhost:5000${selectedImage.posterURL}`}
                  alt={selectedImage.title}
                  className="img-fluid w-100"
                  style={{ maxHeight: '80vh', objectFit: 'contain' }}
                />
                <div className="position-absolute top-0 start-0 m-3">
                  <span className={`badge bg-${getCategoryColor(selectedImage.category)} fs-6`}>
                    {selectedImage.category}
                  </span>
                </div>
              </div>
            )}
          </Modal.Body>
          <Modal.Footer className="border-0 justify-content-between">
            <div className="text-start">
              <small className="text-muted d-block">
                <i className="fas fa-calendar me-1"></i>
                {selectedImage && new Date(selectedImage.eventDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </small>
              <small className="text-muted d-block">
                <i className="fas fa-map-marker-alt me-1"></i>
                {selectedImage?.venue}
              </small>
            </div>
            <div>
              <Button variant="secondary" onClick={handleCloseModal}>
                Close
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
      </Container>
    </div>
  );
};

export default Gallery;
