import React from 'react';
import { Container, <PERSON>, Col } from 'react-bootstrap';

const Footer = () => {
  return (
    <footer className="bg-dark text-light py-5 mt-5">
      <Container>
        <Row>
          <Col md={4} className="mb-4">
            <h5 className="text-warning fw-bold mb-3">ALTIUS 2K25</h5>
            <p className="text-muted">
              The ultimate college fest bringing together technology, culture, sports, 
              and creativity. Join us for an unforgettable experience filled with 
              competitions, performances, and networking opportunities.
            </p>
          </Col>
          
          <Col md={2} className="mb-4">
            <h6 className="text-warning fw-semibold mb-3">Quick Links</h6>
            <ul className="list-unstyled">
              <li><a href="/" className="text-muted text-decoration-none hover-warning">Home</a></li>
              <li><a href="/events" className="text-muted text-decoration-none hover-warning">Events</a></li>
              <li><a href="/gallery" className="text-muted text-decoration-none hover-warning">Gallery</a></li>
              <li><a href="/register" className="text-muted text-decoration-none hover-warning">Register</a></li>
            </ul>
          </Col>
          
          <Col md={3} className="mb-4">
            <h6 className="text-warning fw-semibold mb-3">Event Categories</h6>
            <ul className="list-unstyled">
              <li><span className="text-muted">Technical Events</span></li>
              <li><span className="text-muted">Cultural Programs</span></li>
              <li><span className="text-muted">Sports Competitions</span></li>
              <li><span className="text-muted">Gaming Tournaments</span></li>
              <li><span className="text-muted">Literary Events</span></li>
            </ul>
          </Col>
          
          <Col md={3} className="mb-4">
            <h6 className="text-warning fw-semibold mb-3">Contact Info</h6>
            <div className="text-muted">
              <p className="mb-2">
                <i className="fas fa-map-marker-alt me-2"></i>
                College Campus, Main Auditorium
              </p>
              <p className="mb-2">
                <i className="fas fa-envelope me-2"></i>
                <EMAIL>
              </p>
              <p className="mb-2">
                <i className="fas fa-phone me-2"></i>
                +91 98765 43210
              </p>
            </div>
            
            <div className="mt-3">
              <h6 className="text-warning fw-semibold mb-2">Follow Us</h6>
              <div className="d-flex gap-3">
                <a href="#" className="text-muted hover-warning">
                  <i className="fab fa-facebook-f fs-5"></i>
                </a>
                <a href="#" className="text-muted hover-warning">
                  <i className="fab fa-twitter fs-5"></i>
                </a>
                <a href="#" className="text-muted hover-warning">
                  <i className="fab fa-instagram fs-5"></i>
                </a>
                <a href="#" className="text-muted hover-warning">
                  <i className="fab fa-linkedin-in fs-5"></i>
                </a>
              </div>
            </div>
          </Col>
        </Row>
        
        <hr className="my-4 border-secondary" />
        
        <Row className="align-items-center">
          <Col md={6}>
            <p className="text-muted mb-0">
              &copy; 2025 ALTIUS 2K25. All rights reserved.
            </p>
          </Col>
          <Col md={6} className="text-md-end">
            <p className="text-muted mb-0">
              Made with <i className="fas fa-heart text-danger"></i> by the ALTIUS Team
            </p>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;
