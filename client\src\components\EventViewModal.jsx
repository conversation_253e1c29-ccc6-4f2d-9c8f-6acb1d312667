import React from 'react';
import { Mo<PERSON>, Row, Col, Badge, Table, Button } from 'react-bootstrap';
import { formatDate, getCategoryColor } from '../utils/helpers';

const EventViewModal = ({ show, onHide, event, onEdit }) => {
  if (!event) return null;

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>Event Details</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Row>
          <Col md={8}>
            <h4 className="mb-3">{event.title}</h4>
            <div className="mb-3">
              <Badge bg={getCategoryColor(event.category)} className="me-2">
                {event.category}
              </Badge>
              <Badge bg={event.isActive ? 'success' : 'secondary'}>
                {event.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            
            <div className="mb-4">
              <h6 className="text-muted mb-2">Description</h6>
              <p className="mb-0">{event.description}</p>
            </div>

            <div className="mb-4">
              <h6 className="text-muted mb-2">Rules</h6>
              <p className="mb-0" style={{ whiteSpace: 'pre-wrap' }}>{event.rules}</p>
            </div>

            <Row className="mb-4">
              <Col md={6}>
                <h6 className="text-muted mb-2">Event Details</h6>
                <Table size="sm" borderless>
                  <tbody>
                    <tr>
                      <td className="fw-semibold">Date:</td>
                      <td>{formatDate(event.eventDate)}</td>
                    </tr>
                    <tr>
                      <td className="fw-semibold">Venue:</td>
                      <td>{event.venue}</td>
                    </tr>
                    <tr>
                      <td className="fw-semibold">Max Participants:</td>
                      <td>{event.maxParticipants}</td>
                    </tr>
                    <tr>
                      <td className="fw-semibold">Registrations:</td>
                      <td>
                        <span className="fw-bold text-primary">
                          {event.registrationCount || 0}
                        </span>
                        <span className="text-muted"> / {event.maxParticipants}</span>
                      </td>
                    </tr>
                  </tbody>
                </Table>
              </Col>
              <Col md={6}>
                <h6 className="text-muted mb-2">Contact Information</h6>
                <Table size="sm" borderless>
                  <tbody>
                    <tr>
                      <td className="fw-semibold">Name:</td>
                      <td>{event.contactInfo?.name}</td>
                    </tr>
                    <tr>
                      <td className="fw-semibold">Email:</td>
                      <td>
                        <a href={`mailto:${event.contactInfo?.email}`}>
                          {event.contactInfo?.email}
                        </a>
                      </td>
                    </tr>
                    <tr>
                      <td className="fw-semibold">Phone:</td>
                      <td>
                        <a href={`tel:${event.contactInfo?.phone}`}>
                          {event.contactInfo?.phone}
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </Table>
              </Col>
            </Row>
          </Col>
          <Col md={4}>
            <div className="text-center">
              <h6 className="text-muted mb-2">Event Poster</h6>
              {event.posterURL ? (
                <img
                  src={`http://localhost:5000${event.posterURL}`}
                  alt={event.title}
                  className="img-fluid rounded shadow-sm"
                  style={{ maxHeight: '300px' }}
                />
              ) : (
                <div 
                  className="bg-light rounded d-flex align-items-center justify-content-center"
                  style={{ height: '200px' }}
                >
                  <span className="text-muted">No poster available</span>
                </div>
              )}
            </div>
          </Col>
        </Row>

        {/* Registration Progress */}
        <div className="mb-3">
          <h6 className="text-muted mb-2">Registration Progress</h6>
          <div className="progress" style={{ height: '8px' }}>
            <div
              className="progress-bar bg-success"
              style={{
                width: `${((event.registrationCount || 0) / event.maxParticipants) * 100}%`
              }}
            ></div>
          </div>
          <small className="text-muted">
            {((event.registrationCount || 0) / event.maxParticipants * 100).toFixed(1)}% filled
          </small>
        </div>

        {/* Additional Information */}
        <Row>
          <Col md={6}>
            <h6 className="text-muted mb-2">Created</h6>
            <p className="mb-0 small">
              {event.createdAt ? formatDate(event.createdAt) : 'N/A'}
            </p>
          </Col>
          <Col md={6}>
            <h6 className="text-muted mb-2">Last Updated</h6>
            <p className="mb-0 small">
              {event.updatedAt ? formatDate(event.updatedAt) : 'N/A'}
            </p>
          </Col>
        </Row>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
        <Button variant="primary" onClick={() => onEdit(event)}>
          <i className="fas fa-edit me-2"></i>
          Edit Event
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default EventViewModal;
