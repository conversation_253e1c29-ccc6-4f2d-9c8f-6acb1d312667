import React, { useState, useEffect } from 'react';
import { <PERSON>dal, Table, But<PERSON>, Badge, Alert, Spinner, Form, InputGroup, Row, Col } from 'react-bootstrap';
import { adminAPI } from '../services/api';
import { formatDate } from '../utils/helpers';
import { toast } from 'react-toastify';

const UserManagementModal = ({ show, onHide }) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all'); // all, admin, regular

  useEffect(() => {
    if (show) {
      fetchUsers();
    }
  }, [show]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await adminAPI.getAllUsers();
      setUsers(response.data.users);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterType === 'all' || 
                         (filterType === 'admin' && user.isAdmin) ||
                         (filterType === 'regular' && !user.isAdmin);
    
    return matchesSearch && matchesFilter;
  });

  const exportUsers = () => {
    try {
      const csvContent = [
        ['Name', 'Email', 'Type', 'Registered Events', 'Join Date'],
        ...users.map(user => [
          user.name,
          user.email,
          user.isAdmin ? 'Admin' : 'Regular',
          user.registeredEventsCount,
          formatDate(user.createdAt)
        ])
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `altius_users_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success('Users list exported successfully!');
    } catch (error) {
      toast.error('Failed to export users list');
    }
  };

  const getUserTypeColor = (isAdmin) => {
    return isAdmin ? 'danger' : 'primary';
  };

  const getEventsBadgeColor = (count) => {
    if (count === 0) return 'secondary';
    if (count <= 2) return 'info';
    if (count <= 5) return 'warning';
    return 'success';
  };

  return (
    <Modal show={show} onHide={onHide} size="xl" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          <i className="fas fa-users-cog me-2"></i>
          User Management
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {/* Stats Cards */}
        <Row className="mb-4">
          <Col md={3}>
            <div className="bg-primary text-white p-3 rounded text-center">
              <h4 className="mb-1">{users.length}</h4>
              <small>Total Users</small>
            </div>
          </Col>
          <Col md={3}>
            <div className="bg-danger text-white p-3 rounded text-center">
              <h4 className="mb-1">{users.filter(u => u.isAdmin).length}</h4>
              <small>Admin Users</small>
            </div>
          </Col>
          <Col md={3}>
            <div className="bg-success text-white p-3 rounded text-center">
              <h4 className="mb-1">{users.filter(u => !u.isAdmin).length}</h4>
              <small>Regular Users</small>
            </div>
          </Col>
          <Col md={3}>
            <div className="bg-info text-white p-3 rounded text-center">
              <h4 className="mb-1">{users.reduce((sum, u) => sum + u.registeredEventsCount, 0)}</h4>
              <small>Total Registrations</small>
            </div>
          </Col>
        </Row>

        {/* Search and Filters */}
        <Row className="mb-3">
          <Col md={6}>
            <InputGroup>
              <InputGroup.Text>
                <i className="fas fa-search"></i>
              </InputGroup.Text>
              <Form.Control
                type="text"
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
          </Col>
          <Col md={3}>
            <Form.Select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              <option value="all">All Users</option>
              <option value="admin">Admin Users</option>
              <option value="regular">Regular Users</option>
            </Form.Select>
          </Col>
          <Col md={3}>
            <div className="d-flex gap-2">
              <Button
                variant="outline-success"
                size="sm"
                onClick={exportUsers}
                disabled={users.length === 0}
              >
                <i className="fas fa-download me-1"></i>
                Export CSV
              </Button>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={fetchUsers}
                disabled={loading}
              >
                <i className="fas fa-sync-alt me-1"></i>
                Refresh
              </Button>
            </div>
          </Col>
        </Row>

        {/* Users Table */}
        {loading ? (
          <div className="text-center py-4">
            <Spinner animation="border" variant="primary" />
            <p className="mt-2 text-muted">Loading users...</p>
          </div>
        ) : users.length === 0 ? (
          <Alert variant="info" className="text-center">
            <i className="fas fa-info-circle me-2"></i>
            No users found.
          </Alert>
        ) : (
          <>
            <div className="mb-2">
              <small className="text-muted">
                Showing {filteredUsers.length} of {users.length} users
              </small>
            </div>
            <div className="table-responsive" style={{ maxHeight: '500px', overflowY: 'auto' }}>
              <Table hover size="sm">
                <thead className="bg-light sticky-top">
                  <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Type</th>
                    <th>Registered Events</th>
                    <th>Join Date</th>
                    <th>Events Details</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user, index) => (
                    <tr key={user.id}>
                      <td>{index + 1}</td>
                      <td>
                        <div className="fw-semibold">{user.name}</div>
                      </td>
                      <td>
                        <a href={`mailto:${user.email}`} className="text-decoration-none">
                          {user.email}
                        </a>
                      </td>
                      <td>
                        <Badge bg={getUserTypeColor(user.isAdmin)}>
                          {user.isAdmin ? 'Admin' : 'Regular'}
                        </Badge>
                      </td>
                      <td>
                        <Badge bg={getEventsBadgeColor(user.registeredEventsCount)}>
                          {user.registeredEventsCount} events
                        </Badge>
                      </td>
                      <td>
                        <small className="text-muted">
                          {formatDate(user.createdAt)}
                        </small>
                      </td>
                      <td>
                        {user.registeredEvents.length > 0 ? (
                          <div>
                            {user.registeredEvents.slice(0, 2).map((event, idx) => (
                              <div key={event.id} className="small text-muted">
                                • {event.title}
                              </div>
                            ))}
                            {user.registeredEvents.length > 2 && (
                              <small className="text-muted">
                                +{user.registeredEvents.length - 2} more...
                              </small>
                            )}
                          </div>
                        ) : (
                          <small className="text-muted">No events</small>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        <div className="d-flex justify-content-between align-items-center w-100">
          <div>
            {users.length > 0 && (
              <Badge bg="primary">
                {users.length} total user{users.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>
          <Button variant="secondary" onClick={onHide}>
            Close
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default UserManagementModal;
