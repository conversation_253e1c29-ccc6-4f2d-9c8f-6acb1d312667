const express = require('express');
const Event = require('../models/Event');
const User = require('../models/User');
const { auth, adminAuth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/events
// @desc    Get all events
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { category, search, page = 1, limit = 10 } = req.query;

    // Fallback events for development (when MongoDB is not available)
    const fallbackEvents = [
      {
        _id: 'event-001',
        title: 'Code Sprint 2025',
        description: 'A 24-hour coding marathon where teams compete to build innovative solutions to real-world problems. Showcase your programming skills and creativity!',
        category: 'Technical',
        eventDate: '2025-02-20T10:00:00Z',
        venue: 'Computer Lab A',
        maxParticipants: 50,
        registeredParticipants: [],
        registrationCount: 12,
        availableSpots: 38,
        posterURL: '/uploads/sample-poster-1.svg',
        contactInfo: {
          name: 'Tech Team',
          email: '<EMAIL>'
        },
        rules: ['Team size: 2-4 members', 'Bring your own laptops', 'Internet will be provided'],
        isActive: true
      },
      {
        _id: 'event-002',
        title: 'Cultural Night',
        description: 'An evening filled with music, dance, and cultural performances. Join us for a celebration of diversity and talent!',
        category: 'Cultural',
        eventDate: '2025-02-22T18:00:00Z',
        venue: 'Main Auditorium',
        maxParticipants: 200,
        registeredParticipants: [],
        registrationCount: 45,
        availableSpots: 155,
        posterURL: '/uploads/sample-poster-2.svg',
        contactInfo: {
          name: 'Cultural Team',
          email: '<EMAIL>'
        },
        rules: ['Performance time limit: 5 minutes', 'Props allowed', 'Original content preferred'],
        isActive: true
      },
      {
        _id: 'event-003',
        title: 'Gaming Tournament',
        description: 'Compete in popular esports titles including VALORANT, CS2, and FIFA. Show your gaming prowess and win exciting prizes!',
        category: 'Gaming',
        eventDate: '2025-02-25T14:00:00Z',
        venue: 'Gaming Arena',
        maxParticipants: 64,
        registeredParticipants: [],
        registrationCount: 28,
        availableSpots: 36,
        posterURL: '/uploads/sample-poster-3.svg',
        contactInfo: {
          name: 'Gaming Team',
          email: '<EMAIL>'
        },
        rules: ['Bring your own peripherals', 'Fair play policy', 'No cheating tolerated'],
        isActive: true
      }
    ];

    // Try database first, fallback to sample data if DB is not available
    try {
      let query = { isActive: true };

      // Filter by category
      if (category && category !== 'All') {
        query.category = category;
      }

      // Search functionality
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      const events = await Event.find(query)
        .populate('registeredParticipants', 'name email')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

      const total = await Event.countDocuments(query);

      res.json({
        success: true,
        events,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        total
      });
    } catch (dbError) {
      // Use fallback events when database is not available
      let filteredEvents = fallbackEvents;

      // Apply category filter
      if (category && category !== 'All') {
        filteredEvents = filteredEvents.filter(event => event.category === category);
      }

      // Apply search filter
      if (search) {
        const searchLower = search.toLowerCase();
        filteredEvents = filteredEvents.filter(event =>
          event.title.toLowerCase().includes(searchLower) ||
          event.description.toLowerCase().includes(searchLower)
        );
      }

      // Apply pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + parseInt(limit);
      const paginatedEvents = filteredEvents.slice(startIndex, endIndex);

      res.json({
        success: true,
        events: paginatedEvents,
        totalPages: Math.ceil(filteredEvents.length / limit),
        currentPage: parseInt(page),
        total: filteredEvents.length,
        message: 'Using sample data (development mode)'
      });
    }
  } catch (error) {
    console.error('Get events error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching events'
    });
  }
});

// @route   GET /api/events/:id
// @desc    Get single event
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    // Fallback events for development
    const fallbackEvents = [
      {
        _id: 'event-001',
        title: 'Code Sprint 2025',
        description: 'A 24-hour coding marathon where teams compete to build innovative solutions to real-world problems. Showcase your programming skills and creativity!',
        category: 'Technical',
        eventDate: '2025-02-20T10:00:00Z',
        venue: 'Computer Lab A',
        maxParticipants: 50,
        registeredParticipants: [],
        registrationCount: 12,
        availableSpots: 38,
        posterURL: '/uploads/sample-poster-1.svg',
        contactInfo: {
          name: 'Tech Team',
          email: '<EMAIL>'
        },
        rules: ['Team size: 2-4 members', 'Bring your own laptops', 'Internet will be provided'],
        isActive: true
      },
      {
        _id: 'event-002',
        title: 'Cultural Night',
        description: 'An evening filled with music, dance, and cultural performances. Join us for a celebration of diversity and talent!',
        category: 'Cultural',
        eventDate: '2025-02-22T18:00:00Z',
        venue: 'Main Auditorium',
        maxParticipants: 200,
        registeredParticipants: [],
        registrationCount: 45,
        availableSpots: 155,
        posterURL: '/uploads/sample-poster-2.svg',
        contactInfo: {
          name: 'Cultural Team',
          email: '<EMAIL>'
        },
        rules: ['Performance time limit: 5 minutes', 'Props allowed', 'Original content preferred'],
        isActive: true
      },
      {
        _id: 'event-003',
        title: 'Gaming Tournament',
        description: 'Compete in popular esports titles including VALORANT, CS2, and FIFA. Show your gaming prowess and win exciting prizes!',
        category: 'Gaming',
        eventDate: '2025-02-25T14:00:00Z',
        venue: 'Gaming Arena',
        maxParticipants: 64,
        registeredParticipants: [],
        registrationCount: 28,
        availableSpots: 36,
        posterURL: '/uploads/sample-poster-3.svg',
        contactInfo: {
          name: 'Gaming Team',
          email: '<EMAIL>'
        },
        rules: ['Bring your own peripherals', 'Fair play policy', 'No cheating tolerated'],
        isActive: true
      }
    ];

    try {
      const event = await Event.findById(req.params.id)
        .populate('registeredParticipants', 'name email');

      if (!event) {
        return res.status(404).json({
          success: false,
          message: 'Event not found'
        });
      }

      res.json({
        success: true,
        event
      });
    } catch (dbError) {
      // Use fallback events when database is not available
      const event = fallbackEvents.find(e => e._id === req.params.id);

      if (!event) {
        return res.status(404).json({
          success: false,
          message: 'Event not found'
        });
      }

      res.json({
        success: true,
        event,
        message: 'Using sample data (development mode)'
      });
    }
  } catch (error) {
    console.error('Get event error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching event'
    });
  }
});

// @route   POST /api/events/:id/register
// @desc    Register for an event
// @access  Private
router.post('/:id/register', auth, async (req, res) => {
  try {
    // For development mode without database, simulate successful registration
    const fallbackEventIds = ['event-001', 'event-002', 'event-003'];

    try {
      const event = await Event.findById(req.params.id);

      if (!event) {
        return res.status(404).json({
          success: false,
          message: 'Event not found'
        });
      }

      // Check if user is already registered
      if (event.registeredParticipants.includes(req.user._id)) {
        return res.status(400).json({
          success: false,
          message: 'You are already registered for this event'
        });
      }

      // Check if event is full
      if (event.registeredParticipants.length >= event.maxParticipants) {
        return res.status(400).json({
          success: false,
          message: 'Event is full'
        });
      }

      // Register user for event
      event.registeredParticipants.push(req.user._id);
      await event.save();

      // Add event to user's registered events
      const user = await User.findById(req.user._id);
      user.registeredEvents.push(event._id);
      await user.save();

      res.json({
        success: true,
        message: 'Successfully registered for the event'
      });
    } catch (dbError) {
      // Fallback for development mode
      if (fallbackEventIds.includes(req.params.id)) {
        res.json({
          success: true,
          message: 'Successfully registered for the event (development mode)'
        });
      } else {
        return res.status(404).json({
          success: false,
          message: 'Event not found'
        });
      }
    }
  } catch (error) {
    console.error('Event registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during registration'
    });
  }
});

// @route   DELETE /api/events/:id/unregister
// @desc    Unregister from an event
// @access  Private
router.delete('/:id/unregister', auth, async (req, res) => {
  try {
    // For development mode without database, simulate successful unregistration
    const fallbackEventIds = ['event-001', 'event-002', 'event-003'];

    try {
      const event = await Event.findById(req.params.id);

      if (!event) {
        return res.status(404).json({
          success: false,
          message: 'Event not found'
        });
      }

      // Check if user is registered
      if (!event.registeredParticipants.includes(req.user._id)) {
        return res.status(400).json({
          success: false,
          message: 'You are not registered for this event'
        });
      }

      // Remove user from event
      event.registeredParticipants = event.registeredParticipants.filter(
        participant => participant.toString() !== req.user._id.toString()
      );
      await event.save();

      // Remove event from user's registered events
      const user = await User.findById(req.user._id);
      user.registeredEvents = user.registeredEvents.filter(
        eventId => eventId.toString() !== event._id.toString()
      );
      await user.save();

      res.json({
        success: true,
        message: 'Successfully unregistered from the event'
      });
    } catch (dbError) {
      // Fallback for development mode
      if (fallbackEventIds.includes(req.params.id)) {
        res.json({
          success: true,
          message: 'Successfully unregistered from the event (development mode)'
        });
      } else {
        return res.status(404).json({
          success: false,
          message: 'Event not found'
        });
      }
    }
  } catch (error) {
    console.error('Event unregistration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during unregistration'
    });
  }
});

module.exports = router;
