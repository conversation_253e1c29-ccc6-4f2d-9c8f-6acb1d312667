import React from 'react';
import { Spin<PERSON>, Container, Row, Col } from 'react-bootstrap';

const Loading = ({ size = 'lg', text = 'Loading...', fullScreen = false }) => {
  if (fullScreen) {
    return (
      <Container fluid className="d-flex align-items-center justify-content-center min-vh-100">
        <Row>
          <Col className="text-center">
            <Spinner animation="border" variant="warning" size={size} />
            <div className="mt-3">
              <h5 className="text-muted">{text}</h5>
            </div>
          </Col>
        </Row>
      </Container>
    );
  }

  return (
    <div className="text-center py-5">
      <Spinner animation="border" variant="warning" size={size} />
      <div className="mt-3">
        <span className="text-muted">{text}</span>
      </div>
    </div>
  );
};

export default Loading;
