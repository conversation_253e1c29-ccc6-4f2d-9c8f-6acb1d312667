const jwt = require('jsonwebtoken');
const User = require('../models/User');

const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }

    const jwtSecret = process.env.JWT_SECRET || 'fallback_jwt_secret_for_development_only';
    const decoded = jwt.verify(token, jwtSecret);

    // Fallback users for development (when MongoDB is not available)
    const fallbackUsers = [
      {
        _id: 'admin-001',
        id: 'admin-001',
        name: 'Admin User',
        email: '<EMAIL>',
        isAdmin: true,
        registeredEvents: []
      },
      {
        _id: 'user-001',
        id: 'user-001',
        name: 'Test User',
        email: '<EMAIL>',
        isAdmin: false,
        registeredEvents: []
      }
    ];

    // Check if this is a fallback user
    const fallbackUser = fallbackUsers.find(u => u.id === decoded.userId);
    if (fallbackUser) {
      req.user = fallbackUser;
      return next();
    }

    // Try database lookup for real users
    try {
      const user = await User.findById(decoded.userId).select('-password');

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Token is not valid.'
        });
      }

      req.user = user;
      next();
    } catch (dbError) {
      // If database is not available and not a fallback user
      return res.status(401).json({
        success: false,
        message: 'Token is not valid.'
      });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({
      success: false,
      message: 'Token is not valid.'
    });
  }
};

const adminAuth = async (req, res, next) => {
  try {
    await auth(req, res, () => {
      if (!req.user.isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Admin privileges required.'
        });
      }
      next();
    });
  } catch (error) {
    console.error('Admin auth middleware error:', error);
    res.status(403).json({
      success: false,
      message: 'Access denied. Admin privileges required.'
    });
  }
};

module.exports = { auth, adminAuth };
