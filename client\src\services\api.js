import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (credentials) => api.post('/auth/login', credentials),
  getProfile: () => api.get('/auth/me'),
};

// Events API calls
export const eventsAPI = {
  getAllEvents: (params = {}) => api.get('/events', { params }),
  getEvent: (id) => api.get(`/events/${id}`),
  registerForEvent: (id) => api.post(`/events/${id}/register`),
  unregisterFromEvent: (id) => api.delete(`/events/${id}/unregister`),
};

// Admin API calls
export const adminAPI = {
  createEvent: (eventData) => {
    const formData = new FormData();
    Object.keys(eventData).forEach(key => {
      if (key === 'poster') {
        formData.append('poster', eventData[key]);
      } else {
        formData.append(key, eventData[key]);
      }
    });
    return api.post('/admin/events', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  updateEvent: (id, eventData) => {
    const formData = new FormData();
    Object.keys(eventData).forEach(key => {
      if (key === 'poster' && eventData[key]) {
        formData.append('poster', eventData[key]);
      } else {
        formData.append(key, eventData[key]);
      }
    });
    return api.put(`/admin/events/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  deleteEvent: (id) => api.delete(`/admin/events/${id}`),
  getDashboard: () => api.get('/admin/dashboard'),
  getEventParticipants: (eventId) => api.get(`/admin/events/${eventId}/participants`),
  removeParticipant: (eventId, userId) => api.delete(`/admin/events/${eventId}/participants/${userId}`),
  getAllUsers: () => api.get('/admin/users'),
};

export default api;
