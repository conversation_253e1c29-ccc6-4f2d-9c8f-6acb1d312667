# ALTIUS 2K25 - Development Guide

## 🚀 Quick Start

### Running the Application

1. **Start Backend Server**:
   ```bash
   cd server
   npm run dev
   ```
   Server runs on: `http://localhost:5000`

2. **Start Frontend Server**:
   ```bash
   cd client
   npx vite
   ```
   Frontend runs on: `http://localhost:5173`

## 🔐 Test Credentials (Development Mode)

When MongoDB is not available, the application uses fallback authentication:

### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Access**: Full admin privileges, can manage events

### Regular User Account
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Access**: Standard user features, can register for events

## 🛠️ Development Features

### Without Database
- ✅ User authentication (fallback system)
- ✅ Frontend routing and navigation
- ✅ UI components and styling
- ❌ Event data persistence
- ❌ User registration persistence

### With Database (MongoDB)
- ✅ Full authentication system
- ✅ Event management
- ✅ User registration and profiles
- ✅ Data persistence

## 🔧 Database Setup (Optional)

To enable full functionality:

1. **Install MongoDB locally** OR **Use MongoDB Atlas**
2. **Create `.env` file in server directory**:
   ```
   MONGODB_URI=mongodb://localhost:27017/altius2k25
   JWT_SECRET=your-secret-key-here
   PORT=5000
   ```
3. **Restart the server**

## 📁 Project Structure

```
ALTIUS_2k25/
├── client/          # React frontend
├── server/          # Express backend
└── README.md
```

## 🎯 Key Features

- **Responsive Design**: Bootstrap 5 + custom CSS
- **Authentication**: JWT-based with fallback system
- **Routing**: React Router with protected routes
- **State Management**: React Context API
- **API Integration**: Axios with interceptors
- **File Upload**: Multer for event posters
- **Toast Notifications**: React Toastify

## 🐛 Troubleshooting

### Frontend 404 Errors
- Fixed with `historyApiFallback: true` in Vite config

### Backend Login Errors
- Uses fallback authentication when MongoDB unavailable
- Check server logs for specific error messages

### CORS Issues
- Backend configured with CORS middleware
- Frontend proxy configured in Vite

## 📝 Notes

- Admin credentials are not displayed on login page for security
- Fallback authentication is for development only
- Production deployment requires proper database setup
