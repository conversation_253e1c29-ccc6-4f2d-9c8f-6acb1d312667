const express = require('express');
const multer = require('multer');
const path = require('path');
const Event = require('../models/Event');
const User = require('../models/User');
const { adminAuth } = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'poster-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// @route   POST /api/admin/events
// @desc    Create a new event
// @access  Admin
router.post('/events', adminAuth, upload.single('poster'), async (req, res) => {
  try {
    const {
      title,
      description,
      rules,
      category,
      contactName,
      contactEmail,
      contactPhone,
      maxParticipants,
      eventDate,
      venue
    } = req.body;

    // Validation
    if (!title || !description || !rules || !category || !contactName || 
        !contactEmail || !contactPhone || !eventDate || !venue) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }

    let posterURL = '';
    if (req.file) {
      posterURL = `/uploads/${req.file.filename}`;
    } else {
      return res.status(400).json({
        success: false,
        message: 'Poster image is required'
      });
    }

    const event = new Event({
      title,
      description,
      rules,
      category,
      posterURL,
      contactInfo: {
        name: contactName,
        email: contactEmail,
        phone: contactPhone
      },
      maxParticipants: maxParticipants || 100,
      eventDate: new Date(eventDate),
      venue
    });

    await event.save();

    res.status(201).json({
      success: true,
      message: 'Event created successfully',
      event
    });
  } catch (error) {
    console.error('Create event error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating event'
    });
  }
});

// @route   PUT /api/admin/events/:id
// @desc    Update an event
// @access  Admin
router.put('/events/:id', adminAuth, upload.single('poster'), async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Event not found'
      });
    }

    const {
      title,
      description,
      rules,
      category,
      contactName,
      contactEmail,
      contactPhone,
      maxParticipants,
      eventDate,
      venue,
      isActive
    } = req.body;

    // Update fields
    if (title) event.title = title;
    if (description) event.description = description;
    if (rules) event.rules = rules;
    if (category) event.category = category;
    if (contactName) event.contactInfo.name = contactName;
    if (contactEmail) event.contactInfo.email = contactEmail;
    if (contactPhone) event.contactInfo.phone = contactPhone;
    if (maxParticipants) event.maxParticipants = maxParticipants;
    if (eventDate) event.eventDate = new Date(eventDate);
    if (venue) event.venue = venue;
    if (isActive !== undefined) event.isActive = isActive;

    // Update poster if new file uploaded
    if (req.file) {
      event.posterURL = `/uploads/${req.file.filename}`;
    }

    await event.save();

    res.json({
      success: true,
      message: 'Event updated successfully',
      event
    });
  } catch (error) {
    console.error('Update event error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating event'
    });
  }
});

// @route   DELETE /api/admin/events/:id
// @desc    Delete an event
// @access  Admin
router.delete('/events/:id', adminAuth, async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Event not found'
      });
    }

    // Remove event from all users' registered events
    await User.updateMany(
      { registeredEvents: event._id },
      { $pull: { registeredEvents: event._id } }
    );

    await Event.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Event deleted successfully'
    });
  } catch (error) {
    console.error('Delete event error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting event'
    });
  }
});

// @route   GET /api/admin/dashboard
// @desc    Get admin dashboard data
// @access  Admin
router.get('/dashboard', adminAuth, async (req, res) => {
  try {
    // Fallback data for development mode
    const fallbackStats = {
      totalEvents: 3,
      totalUsers: 2,
      activeEvents: 3,
      totalRegistrations: 85
    };

    const fallbackEvents = [
      {
        id: 'event-001',
        title: 'Code Sprint 2025',
        category: 'Technical',
        registrationCount: 12,
        maxParticipants: 50,
        eventDate: '2025-02-20T10:00:00Z',
        isActive: true
      },
      {
        id: 'event-002',
        title: 'Cultural Night',
        category: 'Cultural',
        registrationCount: 45,
        maxParticipants: 200,
        eventDate: '2025-02-22T18:00:00Z',
        isActive: true
      },
      {
        id: 'event-003',
        title: 'Gaming Tournament',
        category: 'Gaming',
        registrationCount: 28,
        maxParticipants: 64,
        eventDate: '2025-02-25T14:00:00Z',
        isActive: true
      }
    ];

    try {
      const totalEvents = await Event.countDocuments();
      const totalUsers = await User.countDocuments();
      const activeEvents = await Event.countDocuments({ isActive: true });

      const events = await Event.find()
        .populate('registeredParticipants', 'name email createdAt')
        .sort({ createdAt: -1 });

      const eventStats = events.map(event => ({
        id: event._id,
        title: event.title,
        description: event.description,
        rules: event.rules,
        category: event.category,
        posterURL: event.posterURL,
        contactInfo: event.contactInfo,
        venue: event.venue,
        registrationCount: event.registeredParticipants.length,
        maxParticipants: event.maxParticipants,
        eventDate: event.eventDate,
        isActive: event.isActive,
        createdAt: event.createdAt,
        updatedAt: event.updatedAt,
        registeredParticipants: event.registeredParticipants.map(user => ({
          id: user._id,
          name: user.name,
          email: user.email,
          registeredAt: user.createdAt
        }))
      }));

      res.json({
        success: true,
        stats: {
          totalEvents,
          totalUsers,
          activeEvents,
          totalRegistrations: events.reduce((sum, event) => sum + event.registeredParticipants.length, 0)
        },
        events: eventStats
      });
    } catch (dbError) {
      // Use fallback data when database is not available
      res.json({
        success: true,
        stats: fallbackStats,
        events: fallbackEvents,
        message: 'Using sample data (development mode)'
      });
    }
  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching dashboard data'
    });
  }
});

// @route   GET /api/admin/events/:id/participants
// @desc    Get all participants for a specific event
// @access  Admin
router.get('/events/:id/participants', adminAuth, async (req, res) => {
  try {
    const event = await Event.findById(req.params.id)
      .populate('registeredParticipants', 'name email createdAt');

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Event not found'
      });
    }

    const participants = event.registeredParticipants.map(user => ({
      id: user._id,
      name: user.name,
      email: user.email,
      registeredAt: user.createdAt
    }));

    res.json({
      success: true,
      event: {
        id: event._id,
        title: event.title,
        maxParticipants: event.maxParticipants,
        registrationCount: event.registeredParticipants.length
      },
      participants
    });
  } catch (error) {
    console.error('Get participants error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching participants'
    });
  }
});

// @route   DELETE /api/admin/events/:eventId/participants/:userId
// @desc    Remove a participant from an event
// @access  Admin
router.delete('/events/:eventId/participants/:userId', adminAuth, async (req, res) => {
  try {
    const { eventId, userId } = req.params;

    // Remove user from event's registered participants
    const event = await Event.findById(eventId);
    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Event not found'
      });
    }

    event.registeredParticipants = event.registeredParticipants.filter(
      participant => participant.toString() !== userId
    );
    await event.save();

    // Remove event from user's registered events
    const user = await User.findById(userId);
    if (user) {
      user.registeredEvents = user.registeredEvents.filter(
        eventRef => eventRef.toString() !== eventId
      );
      await user.save();
    }

    res.json({
      success: true,
      message: 'Participant removed successfully'
    });
  } catch (error) {
    console.error('Remove participant error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while removing participant'
    });
  }
});

// @route   GET /api/admin/users
// @desc    Get all users with their registered events
// @access  Admin
router.get('/users', adminAuth, async (req, res) => {
  try {
    const users = await User.find()
      .populate('registeredEvents', 'title eventDate')
      .select('-password')
      .sort({ createdAt: -1 });

    const userStats = users.map(user => ({
      id: user._id,
      name: user.name,
      email: user.email,
      isAdmin: user.isAdmin,
      registeredEventsCount: user.registeredEvents.length,
      registeredEvents: user.registeredEvents.map(event => ({
        id: event._id,
        title: event.title,
        eventDate: event.eventDate
      })),
      createdAt: user.createdAt
    }));

    res.json({
      success: true,
      users: userStats,
      totalUsers: users.length
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching users'
    });
  }
});

module.exports = router;
